import mixpanel, { Dict } from "mixpanel-browser";
import { mixpanelToken as token } from "../network/endpoints";

const SESSION_RECORDING_KEY = "mixpanel_session_recording_started";

if (token) {
  mixpanel.init(token, {
    record_sessions_percent: 0.5,
  });
}

const actions = {
  identify: (id: string) => {
    mixpanel.identify(id);

    const sessionRecordingStarted =
      localStorage.getItem(SESSION_RECORDING_KEY) === "true";

    if (!sessionRecordingStarted) {
      mixpanel.start_session_recording();
      localStorage.setItem(SESSION_RECORDING_KEY, "true");
    }
  },
  alias: (id: string) => {
    mixpanel.alias(id);
  },
  track: (name: string, props: Dict = {}) => {
    mixpanel.track(name, props);
  },
  people: {
    set: (props: Dict) => {
      mixpanel.people.set(props);
    },
  },
  register: (props: Dict) => {
    mixpanel.register(props);
  },
  reset: () => {
    mixpanel.reset();
  },
};

export const Mixpanel = actions;
