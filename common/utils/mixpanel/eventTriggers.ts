import { Dict } from "mixpanel-browser";
import { Mixpanel } from ".";
import { 
  MixpanelEventName, UserEventProps, CustomEventProps,
} from "./types";
import { v4 as uuidv4 } from "uuid";
import { 
  randomUserIdKey, userEmailKey,
} from "@/common/constants";
import { getCookie } from "cookies-next";
export type { Dict };
export { MixpanelEventName };

const LAST_IDENTIFIED_USER_KEY = "mixpanel_last_identified_user";

export const mixpanelUserEvent = ({
  mixpanelProps = {},
  id,
  eventName,
}: UserEventProps) => {
  try {
    const userId = id?.toString();
    const lastIdentifiedUser = localStorage.getItem(LAST_IDENTIFIED_USER_KEY);

    if (userId && userId !== lastIdentifiedUser) {
      Mixpanel.identify(userId);
      localStorage.setItem(LAST_IDENTIFIED_USER_KEY, userId);
      Mixpanel.people.set(mixpanelProps);
    }

    Mixpanel.track(eventName, mixpanelProps);
  } catch (err) {
    console.error("error", `Mixpanel tracking error: ${err}`);
  }
};

export const mixpanelCustomEvent = ({
  mixpanelProps = {},
  eventName,
}: CustomEventProps) => {
  try {
    const savedUserId = localStorage.getItem(randomUserIdKey);
    const userId = savedUserId || uuidv4();
    const userEmail = getCookie(userEmailKey);
    const lastIdentifiedUser = localStorage.getItem(LAST_IDENTIFIED_USER_KEY);

    let userProps: any = {
      ...mixpanelProps,
      $user_id: userId,
    };

    if (userEmail) {
      userProps = {
        ...mixpanelProps,
        $email: userEmail,
        $user_id: userId,
      };
    }

    if (!savedUserId) {
      localStorage.setItem(randomUserIdKey, userId);
    }

    if (userId && userId !== lastIdentifiedUser) {
      if (lastIdentifiedUser) {
        Mixpanel.reset();
      }
      Mixpanel.identify(userId);
      localStorage.setItem(LAST_IDENTIFIED_USER_KEY, userId);
      Mixpanel.people.set(userProps);
    }

    Mixpanel.track(eventName, userProps);
  } catch (err) {
    console.error("error", `Mixpanel tracking error: ${err}`);
  }
};
